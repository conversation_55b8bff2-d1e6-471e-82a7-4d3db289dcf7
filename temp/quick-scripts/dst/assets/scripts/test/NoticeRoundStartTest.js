
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/NoticeRoundStartTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1b2cPU5fZ4kKvN7xI0VniQ', 'NoticeRoundStartTest');
// scripts/test/NoticeRoundStartTest.ts

"use strict";
// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeRoundStartTest = /** @class */ (function (_super) {
    __extends(NoticeRoundStartTest, _super);
    function NoticeRoundStartTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.firstChoiceTestButton = null;
        _this.statusLabel = null;
        return _this;
    }
    NoticeRoundStartTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试消息';
        }
    };
    // 发送测试的NoticeRoundStart消息
    NoticeRoundStartTest.prototype.sendTestMessage = function () {
        console.log('发送测试NoticeRoundStart消息');
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001\u6D4B\u8BD5\u6D88\u606F: \u56DE\u5408" + testData.roundNumber + ", \u5012\u8BA1\u65F6" + testData.countDown + "\u79D2";
        }
    };
    // 发送倒计时更新测试
    NoticeRoundStartTest.prototype.sendCountdownUpdate = function (seconds) {
        var testData = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5012\u8BA1\u65F6\u66F4\u65B0: " + seconds + "\u79D2";
        }
    };
    // 测试不同的倒计时值
    NoticeRoundStartTest.prototype.testDifferentCountdowns = function () {
        var _this = this;
        // 测试25秒倒计时
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(25);
        }, 1);
        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(20);
        }, 3);
        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(5);
        }, 5);
        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(0);
        }, 7);
    };
    // 发送NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayMessage = function () {
        console.log('发送测试NoticeActionDisplay消息');
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 1,
                    isFirstChoice: true,
                    result: 2 // 数字2
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 5,
                "player_002": 3
            },
            remainingMines: 10,
            message: "展示阶段：显示所有玩家操作"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5C55\u793A\u9636\u6BB5\uFF0C\u5269\u4F59" + testData.countDown + "\u79D2\uFF0C\u5269\u4F59\u70B8\u5F39" + testData.remainingMines + "\u4E2A";
        }
    };
    // 测试完整的回合流程
    NoticeRoundStartTest.prototype.testFullRoundFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 20秒后发送操作展示
        this.scheduleOnce(function () {
            _this.sendActionDisplayMessage();
        }, 2);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    };
    // 测试先手奖励流程
    NoticeRoundStartTest.prototype.testFirstChoiceBonusFlow = function () {
        var _this = this;
        console.log('开始测试先手奖励流程');
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 2秒后发送先手奖励
        this.scheduleOnce(function () {
            _this.sendFirstChoiceBonusMessage();
        }, 2);
        // 3. 4秒后发送操作展示（包含先手玩家）
        this.scheduleOnce(function () {
            _this.sendActionDisplayWithFirstChoiceMessage();
        }, 4);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试先手奖励流程...';
        }
    };
    // 发送NoticeFirstChoiceBonus测试消息
    NoticeRoundStartTest.prototype.sendFirstChoiceBonusMessage = function () {
        console.log('发送测试NoticeFirstChoiceBonus消息');
        var testData = {
            userId: "player_001",
            roundNumber: 1,
            bonusScore: 1,
            totalScore: 6 // 原来5分 + 1分先手奖励
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001FirstChoiceBonus\u6D88\u606F: player_001\u83B7\u5F97+1\u5148\u624B\u5956\u52B1";
        }
    };
    // 发送包含先手玩家的NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayWithFirstChoiceMessage = function () {
        console.log('发送包含先手玩家的NoticeActionDisplay消息');
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 2,
                    isFirstChoice: true,
                    result: 3 // 数字3
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 8,
                "player_002": 4 // 原来3分 + 本回合1分
            },
            remainingMines: 9,
            message: "展示阶段：先手玩家不应在player_game_pfb中显示加减分"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5148\u624B\u73A9\u5BB6\u4E0D\u5E94\u663E\u793Aplayer_game_pfb\u52A0\u51CF\u5206";
        }
    };
    NoticeRoundStartTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "firstChoiceTestButton", void 0);
    __decorate([
        property(cc.Label)
    ], NoticeRoundStartTest.prototype, "statusLabel", void 0);
    NoticeRoundStartTest = __decorate([
        ccclass
    ], NoticeRoundStartTest);
    return NoticeRoundStartTest;
}(cc.Component));
exports.default = NoticeRoundStartTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL3Rlc3QvTm90aWNlUm91bmRTdGFydFRlc3QudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDRCQUE0QjtBQUM1Qiw2Q0FBNkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU3Qyw2Q0FBNEM7QUFDNUMscURBQWtEO0FBQ2xELDhDQUE2QztBQUd2QyxJQUFBLEtBQXdCLEVBQUUsQ0FBQyxVQUFVLEVBQW5DLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBa0IsQ0FBQztBQUc1QztJQUFrRCx3Q0FBWTtJQUE5RDtRQUFBLHFFQTJRQztRQXhRRyxnQkFBVSxHQUFjLElBQUksQ0FBQztRQUc3QiwyQkFBcUIsR0FBYyxJQUFJLENBQUM7UUFHeEMsaUJBQVcsR0FBYSxJQUFJLENBQUM7O0lBa1FqQyxDQUFDO0lBaFFHLG9DQUFLLEdBQUw7UUFDSSxJQUFJLElBQUksQ0FBQyxVQUFVLEVBQUU7WUFDakIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQyxDQUFDO1NBQ2hFO1FBRUQsSUFBSSxJQUFJLENBQUMscUJBQXFCLEVBQUU7WUFDNUIsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyx3QkFBd0IsRUFBRSxJQUFJLENBQUMsQ0FBQztTQUNwRjtRQUVELElBQUksSUFBSSxDQUFDLFdBQVcsRUFBRTtZQUNsQixJQUFJLENBQUMsV0FBVyxDQUFDLE1BQU0sR0FBRyxVQUFVLENBQUM7U0FDeEM7SUFDTCxDQUFDO0lBRUQsMEJBQTBCO0lBQzFCLDhDQUFlLEdBQWY7UUFDSSxPQUFPLENBQUMsR0FBRyxDQUFDLHdCQUF3QixDQUFDLENBQUM7UUFFdEMsU0FBUztRQUNULElBQU0sUUFBUSxHQUFxQjtZQUMvQixXQUFXLEVBQUUsQ0FBQztZQUNkLFNBQVMsRUFBRSxFQUFFO1lBQ2IsVUFBVSxFQUFFLENBQUM7U0FDaEIsQ0FBQztRQUVGLGFBQWE7UUFDYixJQUFNLFdBQVcsR0FBRztZQUNoQixLQUFLLEVBQUUscUJBQVMsQ0FBQyx1QkFBdUI7WUFDeEMsSUFBSSxFQUFFLENBQUM7WUFDUCxHQUFHLEVBQUUsU0FBUztZQUNkLElBQUksRUFBRSxRQUFRO1NBQ2pCLENBQUM7UUFFRixTQUFTO1FBQ1QsaUJBQU8sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLHVCQUFTLENBQUMsY0FBYyxFQUFFLFdBQVcsQ0FBQyxDQUFDO1FBRTFELElBQUksSUFBSSxDQUFDLFdBQVcsRUFBRTtZQUNsQixJQUFJLENBQUMsV0FBVyxDQUFDLE1BQU0sR0FBRyw2REFBYyxRQUFRLENBQUMsV0FBVyw0QkFBUSxRQUFRLENBQUMsU0FBUyxXQUFHLENBQUM7U0FDN0Y7SUFDTCxDQUFDO0lBRUQsWUFBWTtJQUNaLGtEQUFtQixHQUFuQixVQUFvQixPQUFlO1FBQy9CLElBQU0sUUFBUSxHQUFxQjtZQUMvQixXQUFXLEVBQUUsQ0FBQztZQUNkLFNBQVMsRUFBRSxPQUFPO1lBQ2xCLFVBQVUsRUFBRSxDQUFDO1NBQ2hCLENBQUM7UUFFRixJQUFNLFdBQVcsR0FBRztZQUNoQixLQUFLLEVBQUUscUJBQVMsQ0FBQyx1QkFBdUI7WUFDeEMsSUFBSSxFQUFFLENBQUM7WUFDUCxHQUFHLEVBQUUsU0FBUztZQUNkLElBQUksRUFBRSxRQUFRO1NBQ2pCLENBQUM7UUFFRixpQkFBTyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsdUJBQVMsQ0FBQyxjQUFjLEVBQUUsV0FBVyxDQUFDLENBQUM7UUFFMUQsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFO1lBQ2xCLElBQUksQ0FBQyxXQUFXLENBQUMsTUFBTSxHQUFHLHFDQUFVLE9BQU8sV0FBRyxDQUFDO1NBQ2xEO0lBQ0wsQ0FBQztJQUVELFlBQVk7SUFDWixzREFBdUIsR0FBdkI7UUFBQSxpQkFvQkM7UUFuQkcsV0FBVztRQUNYLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDZCxLQUFJLENBQUMsbUJBQW1CLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDakMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRU4sbUJBQW1CO1FBQ25CLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDZCxLQUFJLENBQUMsbUJBQW1CLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDakMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRU4saUJBQWlCO1FBQ2pCLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDZCxLQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDaEMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRU4sZ0JBQWdCO1FBQ2hCLElBQUksQ0FBQyxZQUFZLENBQUM7WUFDZCxLQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDaEMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ1YsQ0FBQztJQUVELDRCQUE0QjtJQUM1Qix1REFBd0IsR0FBeEI7UUFDSSxPQUFPLENBQUMsR0FBRyxDQUFDLDJCQUEyQixDQUFDLENBQUM7UUFFekMsSUFBTSxRQUFRLEdBQXdCO1lBQ2xDLFdBQVcsRUFBRSxDQUFDO1lBQ2QsVUFBVSxFQUFFLENBQUM7WUFDYixTQUFTLEVBQUUsQ0FBQztZQUNaLGFBQWEsRUFBRTtnQkFDWDtvQkFDSSxNQUFNLEVBQUUsWUFBWTtvQkFDcEIsQ0FBQyxFQUFFLENBQUM7b0JBQ0osQ0FBQyxFQUFFLENBQUM7b0JBQ0osTUFBTSxFQUFFLENBQUM7b0JBQ1QsS0FBSyxFQUFFLENBQUM7b0JBQ1IsYUFBYSxFQUFFLElBQUk7b0JBQ25CLE1BQU0sRUFBRSxDQUFDLENBQUMsTUFBTTtpQkFDbkI7Z0JBQ0Q7b0JBQ0ksTUFBTSxFQUFFLFlBQVk7b0JBQ3BCLENBQUMsRUFBRSxDQUFDO29CQUNKLENBQUMsRUFBRSxDQUFDO29CQUNKLE1BQU0sRUFBRSxDQUFDO29CQUNULEtBQUssRUFBRSxDQUFDO29CQUNSLGFBQWEsRUFBRSxLQUFLO29CQUNwQixNQUFNLEVBQUUsY0FBYztpQkFDekI7YUFDSjtZQUNELGlCQUFpQixFQUFFO2dCQUNmLFlBQVksRUFBRSxDQUFDO2dCQUNmLFlBQVksRUFBRSxDQUFDO2FBQ2xCO1lBQ0QsY0FBYyxFQUFFLEVBQUU7WUFDbEIsT0FBTyxFQUFFLGVBQWU7U0FDM0IsQ0FBQztRQUVGLElBQU0sV0FBVyxHQUFHO1lBQ2hCLEtBQUssRUFBRSxxQkFBUyxDQUFDLDBCQUEwQjtZQUMzQyxJQUFJLEVBQUUsQ0FBQztZQUNQLEdBQUcsRUFBRSxTQUFTO1lBQ2QsSUFBSSxFQUFFLFFBQVE7U0FDakIsQ0FBQztRQUVGLGlCQUFPLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyx1QkFBUyxDQUFDLGNBQWMsRUFBRSxXQUFXLENBQUMsQ0FBQztRQUUxRCxJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFDbEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxNQUFNLEdBQUcsNEZBQThCLFFBQVEsQ0FBQyxTQUFTLDRDQUFTLFFBQVEsQ0FBQyxjQUFjLFdBQUcsQ0FBQztTQUNqSDtJQUNMLENBQUM7SUFFRCxZQUFZO0lBQ1osZ0RBQWlCLEdBQWpCO1FBQUEsaUJBWUM7UUFYRyxZQUFZO1FBQ1osSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO1FBRXZCLGdCQUFnQjtRQUNoQixJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ2QsS0FBSSxDQUFDLHdCQUF3QixFQUFFLENBQUM7UUFDcEMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRU4sSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFO1lBQ2xCLElBQUksQ0FBQyxXQUFXLENBQUMsTUFBTSxHQUFHLGVBQWUsQ0FBQztTQUM3QztJQUNMLENBQUM7SUFFRCxXQUFXO0lBQ1gsdURBQXdCLEdBQXhCO1FBQUEsaUJBbUJDO1FBbEJHLE9BQU8sQ0FBQyxHQUFHLENBQUMsWUFBWSxDQUFDLENBQUM7UUFFMUIsWUFBWTtRQUNaLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztRQUV2QixlQUFlO1FBQ2YsSUFBSSxDQUFDLFlBQVksQ0FBQztZQUNkLEtBQUksQ0FBQywyQkFBMkIsRUFBRSxDQUFDO1FBQ3ZDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUVOLHVCQUF1QjtRQUN2QixJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ2QsS0FBSSxDQUFDLHVDQUF1QyxFQUFFLENBQUM7UUFDbkQsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1FBRU4sSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFO1lBQ2xCLElBQUksQ0FBQyxXQUFXLENBQUMsTUFBTSxHQUFHLGVBQWUsQ0FBQztTQUM3QztJQUNMLENBQUM7SUFFRCwrQkFBK0I7SUFDL0IsMERBQTJCLEdBQTNCO1FBQ0ksT0FBTyxDQUFDLEdBQUcsQ0FBQyw4QkFBOEIsQ0FBQyxDQUFDO1FBRTVDLElBQU0sUUFBUSxHQUEyQjtZQUNyQyxNQUFNLEVBQUUsWUFBWTtZQUNwQixXQUFXLEVBQUUsQ0FBQztZQUNkLFVBQVUsRUFBRSxDQUFDO1lBQ2IsVUFBVSxFQUFFLENBQUMsQ0FBQyxnQkFBZ0I7U0FDakMsQ0FBQztRQUVGLElBQU0sV0FBVyxHQUFHO1lBQ2hCLEtBQUssRUFBRSxxQkFBUyxDQUFDLDZCQUE2QjtZQUM5QyxJQUFJLEVBQUUsQ0FBQztZQUNQLEdBQUcsRUFBRSxTQUFTO1lBQ2QsSUFBSSxFQUFFLFFBQVE7U0FDakIsQ0FBQztRQUVGLGlCQUFPLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyx1QkFBUyxDQUFDLGNBQWMsRUFBRSxXQUFXLENBQUMsQ0FBQztRQUUxRCxJQUFJLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFDbEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxNQUFNLEdBQUcsa0dBQTJDLENBQUM7U0FDekU7SUFDTCxDQUFDO0lBRUQsbUNBQW1DO0lBQ25DLHNFQUF1QyxHQUF2QztRQUNJLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0NBQWdDLENBQUMsQ0FBQztRQUU5QyxJQUFNLFFBQVEsR0FBd0I7WUFDbEMsV0FBVyxFQUFFLENBQUM7WUFDZCxVQUFVLEVBQUUsQ0FBQztZQUNiLFNBQVMsRUFBRSxDQUFDO1lBQ1osYUFBYSxFQUFFO2dCQUNYO29CQUNJLE1BQU0sRUFBRSxZQUFZO29CQUNwQixDQUFDLEVBQUUsQ0FBQztvQkFDSixDQUFDLEVBQUUsQ0FBQztvQkFDSixNQUFNLEVBQUUsQ0FBQztvQkFDVCxLQUFLLEVBQUUsQ0FBQztvQkFDUixhQUFhLEVBQUUsSUFBSTtvQkFDbkIsTUFBTSxFQUFFLENBQUMsQ0FBQyxNQUFNO2lCQUNuQjtnQkFDRDtvQkFDSSxNQUFNLEVBQUUsWUFBWTtvQkFDcEIsQ0FBQyxFQUFFLENBQUM7b0JBQ0osQ0FBQyxFQUFFLENBQUM7b0JBQ0osTUFBTSxFQUFFLENBQUM7b0JBQ1QsS0FBSyxFQUFFLENBQUM7b0JBQ1IsYUFBYSxFQUFFLEtBQUs7b0JBQ3BCLE1BQU0sRUFBRSxjQUFjO2lCQUN6QjthQUNKO1lBQ0QsaUJBQWlCLEVBQUU7Z0JBQ2YsWUFBWSxFQUFFLENBQUM7Z0JBQ2YsWUFBWSxFQUFFLENBQUMsQ0FBRSxlQUFlO2FBQ25DO1lBQ0QsY0FBYyxFQUFFLENBQUM7WUFDakIsT0FBTyxFQUFFLG1DQUFtQztTQUMvQyxDQUFDO1FBRUYsSUFBTSxXQUFXLEdBQUc7WUFDaEIsS0FBSyxFQUFFLHFCQUFTLENBQUMsMEJBQTBCO1lBQzNDLElBQUksRUFBRSxDQUFDO1lBQ1AsR0FBRyxFQUFFLFNBQVM7WUFDZCxJQUFJLEVBQUUsUUFBUTtTQUNqQixDQUFDO1FBRUYsaUJBQU8sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLHVCQUFTLENBQUMsY0FBYyxFQUFFLFdBQVcsQ0FBQyxDQUFDO1FBRTFELElBQUksSUFBSSxDQUFDLFdBQVcsRUFBRTtZQUNsQixJQUFJLENBQUMsV0FBVyxDQUFDLE1BQU0sR0FBRyxnSUFBZ0QsQ0FBQztTQUM5RTtJQUNMLENBQUM7SUFFRCx3Q0FBUyxHQUFUO1FBQ0ksSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ2pCLElBQUksQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLGVBQWUsRUFBRSxJQUFJLENBQUMsQ0FBQztTQUNqRTtRQUNELElBQUksSUFBSSxDQUFDLHFCQUFxQixFQUFFO1lBQzVCLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsd0JBQXdCLEVBQUUsSUFBSSxDQUFDLENBQUM7U0FDckY7SUFDTCxDQUFDO0lBdlFEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxNQUFNLENBQUM7NERBQ1M7SUFHN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLE1BQU0sQ0FBQzt1RUFDb0I7SUFHeEM7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQzs2REFDVTtJQVRaLG9CQUFvQjtRQUR4QyxPQUFPO09BQ2Esb0JBQW9CLENBMlF4QztJQUFELDJCQUFDO0NBM1FELEFBMlFDLENBM1FpRCxFQUFFLENBQUMsU0FBUyxHQTJRN0Q7a0JBM1FvQixvQkFBb0IiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyDmtYvor5VOb3RpY2VSb3VuZFN0YXJ05raI5oGv5aSE55CG55qE6ISa5pysXG4vLyDov5nkuKrohJrmnKzlj6/ku6XnlKjmnaXmqKHmi5/lj5HpgIFOb3RpY2VSb3VuZFN0YXJ05raI5oGv77yM5rWL6K+V5YmN56uv6K6h5pe25Zmo5pu05paw5Yqf6IO9XG5cbmltcG9ydCB7IEdhbWVNZ3IgfSBmcm9tIFwiLi4vY29tbW9uL0dhbWVNZ3JcIjtcbmltcG9ydCB7IEV2ZW50VHlwZSB9IGZyb20gXCIuLi9jb21tb24vRXZlbnRDZW50ZXJcIjtcbmltcG9ydCB7IE1lc3NhZ2VJZCB9IGZyb20gXCIuLi9uZXQvTWVzc2FnZUlkXCI7XG5pbXBvcnQgeyBOb3RpY2VSb3VuZFN0YXJ0LCBOb3RpY2VBY3Rpb25EaXNwbGF5LCBOb3RpY2VGaXJzdENob2ljZUJvbnVzIH0gZnJvbSBcIi4uL2JlYW4vR2FtZUJlYW5cIjtcblxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIE5vdGljZVJvdW5kU3RhcnRUZXN0IGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcblxuICAgIEBwcm9wZXJ0eShjYy5CdXR0b24pXG4gICAgdGVzdEJ1dHRvbjogY2MuQnV0dG9uID0gbnVsbDtcblxuICAgIEBwcm9wZXJ0eShjYy5CdXR0b24pXG4gICAgZmlyc3RDaG9pY2VUZXN0QnV0dG9uOiBjYy5CdXR0b24gPSBudWxsO1xuXG4gICAgQHByb3BlcnR5KGNjLkxhYmVsKVxuICAgIHN0YXR1c0xhYmVsOiBjYy5MYWJlbCA9IG51bGw7XG5cbiAgICBzdGFydCgpIHtcbiAgICAgICAgaWYgKHRoaXMudGVzdEJ1dHRvbikge1xuICAgICAgICAgICAgdGhpcy50ZXN0QnV0dG9uLm5vZGUub24oJ2NsaWNrJywgdGhpcy5zZW5kVGVzdE1lc3NhZ2UsIHRoaXMpO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKHRoaXMuZmlyc3RDaG9pY2VUZXN0QnV0dG9uKSB7XG4gICAgICAgICAgICB0aGlzLmZpcnN0Q2hvaWNlVGVzdEJ1dHRvbi5ub2RlLm9uKCdjbGljaycsIHRoaXMudGVzdEZpcnN0Q2hvaWNlQm9udXNGbG93LCB0aGlzKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmICh0aGlzLnN0YXR1c0xhYmVsKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXR1c0xhYmVsLnN0cmluZyA9ICfngrnlh7vmjInpkq7mtYvor5Xmtojmga8nO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Y+R6YCB5rWL6K+V55qETm90aWNlUm91bmRTdGFydOa2iOaBr1xuICAgIHNlbmRUZXN0TWVzc2FnZSgpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+WPkemAgea1i+ivlU5vdGljZVJvdW5kU3RhcnTmtojmga8nKTtcbiAgICAgICAgXG4gICAgICAgIC8vIOWIm+W7uua1i+ivleaVsOaNrlxuICAgICAgICBjb25zdCB0ZXN0RGF0YTogTm90aWNlUm91bmRTdGFydCA9IHtcbiAgICAgICAgICAgIHJvdW5kTnVtYmVyOiAxLFxuICAgICAgICAgICAgY291bnREb3duOiAyNSxcbiAgICAgICAgICAgIGdhbWVTdGF0dXM6IDBcbiAgICAgICAgfTtcblxuICAgICAgICAvLyDmqKHmi5/mjqXmlLbliLDnmoTmtojmga/moLzlvI9cbiAgICAgICAgY29uc3QgbWVzc2FnZUJlYW4gPSB7XG4gICAgICAgICAgICBtc2dJZDogTWVzc2FnZUlkLk1zZ1R5cGVOb3RpY2VSb3VuZFN0YXJ0LFxuICAgICAgICAgICAgY29kZTogMCxcbiAgICAgICAgICAgIG1zZzogXCJzdWNjZXNzXCIsXG4gICAgICAgICAgICBkYXRhOiB0ZXN0RGF0YVxuICAgICAgICB9O1xuXG4gICAgICAgIC8vIOWPkemAgea2iOaBr+S6i+S7tlxuICAgICAgICBHYW1lTWdyLkV2ZW50LlNlbmQoRXZlbnRUeXBlLlJlY2VpdmVNZXNzYWdlLCBtZXNzYWdlQmVhbik7XG4gICAgICAgIFxuICAgICAgICBpZiAodGhpcy5zdGF0dXNMYWJlbCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0dXNMYWJlbC5zdHJpbmcgPSBg5bey5Y+R6YCB5rWL6K+V5raI5oGvOiDlm57lkIgke3Rlc3REYXRhLnJvdW5kTnVtYmVyfSwg5YCS6K6h5pe2JHt0ZXN0RGF0YS5jb3VudERvd25956eSYDtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWPkemAgeWAkuiuoeaXtuabtOaWsOa1i+ivlVxuICAgIHNlbmRDb3VudGRvd25VcGRhdGUoc2Vjb25kczogbnVtYmVyKSB7XG4gICAgICAgIGNvbnN0IHRlc3REYXRhOiBOb3RpY2VSb3VuZFN0YXJ0ID0ge1xuICAgICAgICAgICAgcm91bmROdW1iZXI6IDEsXG4gICAgICAgICAgICBjb3VudERvd246IHNlY29uZHMsXG4gICAgICAgICAgICBnYW1lU3RhdHVzOiAwXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgbWVzc2FnZUJlYW4gPSB7XG4gICAgICAgICAgICBtc2dJZDogTWVzc2FnZUlkLk1zZ1R5cGVOb3RpY2VSb3VuZFN0YXJ0LFxuICAgICAgICAgICAgY29kZTogMCxcbiAgICAgICAgICAgIG1zZzogXCJzdWNjZXNzXCIsXG4gICAgICAgICAgICBkYXRhOiB0ZXN0RGF0YVxuICAgICAgICB9O1xuXG4gICAgICAgIEdhbWVNZ3IuRXZlbnQuU2VuZChFdmVudFR5cGUuUmVjZWl2ZU1lc3NhZ2UsIG1lc3NhZ2VCZWFuKTtcbiAgICAgICAgXG4gICAgICAgIGlmICh0aGlzLnN0YXR1c0xhYmVsKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXR1c0xhYmVsLnN0cmluZyA9IGDlgJLorqHml7bmm7TmlrA6ICR7c2Vjb25kc33np5JgO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5rWL6K+V5LiN5ZCM55qE5YCS6K6h5pe25YC8XG4gICAgdGVzdERpZmZlcmVudENvdW50ZG93bnMoKSB7XG4gICAgICAgIC8vIOa1i+ivlTI156eS5YCS6K6h5pe2XG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuc2VuZENvdW50ZG93blVwZGF0ZSgyNSk7XG4gICAgICAgIH0sIDEpO1xuXG4gICAgICAgIC8vIOa1i+ivlTIw56eS5YCS6K6h5pe277yI6L+b5YWl5bGV56S66Zi25q6177yJXG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuc2VuZENvdW50ZG93blVwZGF0ZSgyMCk7XG4gICAgICAgIH0sIDMpO1xuXG4gICAgICAgIC8vIOa1i+ivlTXnp5LlgJLorqHml7bvvIjlm57lkIjnu5PmnZ/liY3vvIlcbiAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5zZW5kQ291bnRkb3duVXBkYXRlKDUpO1xuICAgICAgICB9LCA1KTtcblxuICAgICAgICAvLyDmtYvor5Uw56eS5YCS6K6h5pe277yI5Zue5ZCI57uT5p2f77yJXG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuc2VuZENvdW50ZG93blVwZGF0ZSgwKTtcbiAgICAgICAgfSwgNyk7XG4gICAgfVxuXG4gICAgLy8g5Y+R6YCBTm90aWNlQWN0aW9uRGlzcGxheea1i+ivlea2iOaBr1xuICAgIHNlbmRBY3Rpb25EaXNwbGF5TWVzc2FnZSgpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+WPkemAgea1i+ivlU5vdGljZUFjdGlvbkRpc3BsYXnmtojmga8nKTtcblxuICAgICAgICBjb25zdCB0ZXN0RGF0YTogTm90aWNlQWN0aW9uRGlzcGxheSA9IHtcbiAgICAgICAgICAgIHJvdW5kTnVtYmVyOiAxLFxuICAgICAgICAgICAgZ2FtZVN0YXR1czogMCxcbiAgICAgICAgICAgIGNvdW50RG93bjogNSxcbiAgICAgICAgICAgIHBsYXllckFjdGlvbnM6IFtcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHVzZXJJZDogXCJwbGF5ZXJfMDAxXCIsXG4gICAgICAgICAgICAgICAgICAgIHg6IDMsXG4gICAgICAgICAgICAgICAgICAgIHk6IDIsXG4gICAgICAgICAgICAgICAgICAgIGFjdGlvbjogMSwgLy8g5oyW5o6YXG4gICAgICAgICAgICAgICAgICAgIHNjb3JlOiAxLFxuICAgICAgICAgICAgICAgICAgICBpc0ZpcnN0Q2hvaWNlOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQ6IDIgLy8g5pWw5a2XMlxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICB1c2VySWQ6IFwicGxheWVyXzAwMlwiLFxuICAgICAgICAgICAgICAgICAgICB4OiAxLFxuICAgICAgICAgICAgICAgICAgICB5OiA0LFxuICAgICAgICAgICAgICAgICAgICBhY3Rpb246IDIsIC8vIOagh+iusFxuICAgICAgICAgICAgICAgICAgICBzY29yZTogMSxcbiAgICAgICAgICAgICAgICAgICAgaXNGaXJzdENob2ljZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdDogXCJjb3JyZWN0X21hcmtcIlxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF0sXG4gICAgICAgICAgICBwbGF5ZXJUb3RhbFNjb3Jlczoge1xuICAgICAgICAgICAgICAgIFwicGxheWVyXzAwMVwiOiA1LFxuICAgICAgICAgICAgICAgIFwicGxheWVyXzAwMlwiOiAzXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgcmVtYWluaW5nTWluZXM6IDEwLCAvLyDliankvZnngrjlvLnmlbDph49cbiAgICAgICAgICAgIG1lc3NhZ2U6IFwi5bGV56S66Zi25q6177ya5pi+56S65omA5pyJ546p5a625pON5L2cXCJcbiAgICAgICAgfTtcblxuICAgICAgICBjb25zdCBtZXNzYWdlQmVhbiA9IHtcbiAgICAgICAgICAgIG1zZ0lkOiBNZXNzYWdlSWQuTXNnVHlwZU5vdGljZUFjdGlvbkRpc3BsYXksXG4gICAgICAgICAgICBjb2RlOiAwLFxuICAgICAgICAgICAgbXNnOiBcInN1Y2Nlc3NcIixcbiAgICAgICAgICAgIGRhdGE6IHRlc3REYXRhXG4gICAgICAgIH07XG5cbiAgICAgICAgR2FtZU1nci5FdmVudC5TZW5kKEV2ZW50VHlwZS5SZWNlaXZlTWVzc2FnZSwgbWVzc2FnZUJlYW4pO1xuXG4gICAgICAgIGlmICh0aGlzLnN0YXR1c0xhYmVsKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXR1c0xhYmVsLnN0cmluZyA9IGDlt7Llj5HpgIFBY3Rpb25EaXNwbGF55raI5oGvOiDlsZXnpLrpmLbmrrXvvIzliankvZkke3Rlc3REYXRhLmNvdW50RG93bn3np5LvvIzliankvZnngrjlvLkke3Rlc3REYXRhLnJlbWFpbmluZ01pbmVzfeS4qmA7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmtYvor5XlrozmlbTnmoTlm57lkIjmtYHnqItcbiAgICB0ZXN0RnVsbFJvdW5kRmxvdygpIHtcbiAgICAgICAgLy8gMS4g5Y+R6YCB5Zue5ZCI5byA5aeLXG4gICAgICAgIHRoaXMuc2VuZFRlc3RNZXNzYWdlKCk7XG5cbiAgICAgICAgLy8gMi4gMjDnp5LlkI7lj5HpgIHmk43kvZzlsZXnpLpcbiAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5zZW5kQWN0aW9uRGlzcGxheU1lc3NhZ2UoKTtcbiAgICAgICAgfSwgMik7XG5cbiAgICAgICAgaWYgKHRoaXMuc3RhdHVzTGFiZWwpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdHVzTGFiZWwuc3RyaW5nID0gJ+W8gOWni+a1i+ivleWujOaVtOWbnuWQiOa1geeoiy4uLic7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmtYvor5XlhYjmiYvlpZblirHmtYHnqItcbiAgICB0ZXN0Rmlyc3RDaG9pY2VCb251c0Zsb3coKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vmtYvor5XlhYjmiYvlpZblirHmtYHnqIsnKTtcblxuICAgICAgICAvLyAxLiDlj5HpgIHlm57lkIjlvIDlp4tcbiAgICAgICAgdGhpcy5zZW5kVGVzdE1lc3NhZ2UoKTtcblxuICAgICAgICAvLyAyLiAy56eS5ZCO5Y+R6YCB5YWI5omL5aWW5YqxXG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuc2VuZEZpcnN0Q2hvaWNlQm9udXNNZXNzYWdlKCk7XG4gICAgICAgIH0sIDIpO1xuXG4gICAgICAgIC8vIDMuIDTnp5LlkI7lj5HpgIHmk43kvZzlsZXnpLrvvIjljIXlkKvlhYjmiYvnjqnlrrbvvIlcbiAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5zZW5kQWN0aW9uRGlzcGxheVdpdGhGaXJzdENob2ljZU1lc3NhZ2UoKTtcbiAgICAgICAgfSwgNCk7XG5cbiAgICAgICAgaWYgKHRoaXMuc3RhdHVzTGFiZWwpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdHVzTGFiZWwuc3RyaW5nID0gJ+W8gOWni+a1i+ivleWFiOaJi+WlluWKsea1geeoiy4uLic7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDlj5HpgIFOb3RpY2VGaXJzdENob2ljZUJvbnVz5rWL6K+V5raI5oGvXG4gICAgc2VuZEZpcnN0Q2hvaWNlQm9udXNNZXNzYWdlKCkge1xuICAgICAgICBjb25zb2xlLmxvZygn5Y+R6YCB5rWL6K+VTm90aWNlRmlyc3RDaG9pY2VCb251c+a2iOaBrycpO1xuXG4gICAgICAgIGNvbnN0IHRlc3REYXRhOiBOb3RpY2VGaXJzdENob2ljZUJvbnVzID0ge1xuICAgICAgICAgICAgdXNlcklkOiBcInBsYXllcl8wMDFcIixcbiAgICAgICAgICAgIHJvdW5kTnVtYmVyOiAxLFxuICAgICAgICAgICAgYm9udXNTY29yZTogMSxcbiAgICAgICAgICAgIHRvdGFsU2NvcmU6IDYgLy8g5Y6f5p2lNeWIhiArIDHliIblhYjmiYvlpZblirFcbiAgICAgICAgfTtcblxuICAgICAgICBjb25zdCBtZXNzYWdlQmVhbiA9IHtcbiAgICAgICAgICAgIG1zZ0lkOiBNZXNzYWdlSWQuTXNnVHlwZU5vdGljZUZpcnN0Q2hvaWNlQm9udXMsXG4gICAgICAgICAgICBjb2RlOiAwLFxuICAgICAgICAgICAgbXNnOiBcInN1Y2Nlc3NcIixcbiAgICAgICAgICAgIGRhdGE6IHRlc3REYXRhXG4gICAgICAgIH07XG5cbiAgICAgICAgR2FtZU1nci5FdmVudC5TZW5kKEV2ZW50VHlwZS5SZWNlaXZlTWVzc2FnZSwgbWVzc2FnZUJlYW4pO1xuXG4gICAgICAgIGlmICh0aGlzLnN0YXR1c0xhYmVsKSB7XG4gICAgICAgICAgICB0aGlzLnN0YXR1c0xhYmVsLnN0cmluZyA9IGDlt7Llj5HpgIFGaXJzdENob2ljZUJvbnVz5raI5oGvOiBwbGF5ZXJfMDAx6I635b6XKzHlhYjmiYvlpZblirFgO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5Y+R6YCB5YyF5ZCr5YWI5omL546p5a6255qETm90aWNlQWN0aW9uRGlzcGxheea1i+ivlea2iOaBr1xuICAgIHNlbmRBY3Rpb25EaXNwbGF5V2l0aEZpcnN0Q2hvaWNlTWVzc2FnZSgpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+WPkemAgeWMheWQq+WFiOaJi+eOqeWutueahE5vdGljZUFjdGlvbkRpc3BsYXnmtojmga8nKTtcblxuICAgICAgICBjb25zdCB0ZXN0RGF0YTogTm90aWNlQWN0aW9uRGlzcGxheSA9IHtcbiAgICAgICAgICAgIHJvdW5kTnVtYmVyOiAxLFxuICAgICAgICAgICAgZ2FtZVN0YXR1czogMCxcbiAgICAgICAgICAgIGNvdW50RG93bjogNSxcbiAgICAgICAgICAgIHBsYXllckFjdGlvbnM6IFtcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHVzZXJJZDogXCJwbGF5ZXJfMDAxXCIsXG4gICAgICAgICAgICAgICAgICAgIHg6IDMsXG4gICAgICAgICAgICAgICAgICAgIHk6IDIsXG4gICAgICAgICAgICAgICAgICAgIGFjdGlvbjogMSwgLy8g5oyW5o6YXG4gICAgICAgICAgICAgICAgICAgIHNjb3JlOiAyLCAvLyDmnKzlm57lkIjlvpfliIZcbiAgICAgICAgICAgICAgICAgICAgaXNGaXJzdENob2ljZTogdHJ1ZSwgLy8g5YWI5omL546p5a62XG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdDogMyAvLyDmlbDlrZczXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHVzZXJJZDogXCJwbGF5ZXJfMDAyXCIsXG4gICAgICAgICAgICAgICAgICAgIHg6IDEsXG4gICAgICAgICAgICAgICAgICAgIHk6IDQsXG4gICAgICAgICAgICAgICAgICAgIGFjdGlvbjogMiwgLy8g5qCH6K6wXG4gICAgICAgICAgICAgICAgICAgIHNjb3JlOiAxLFxuICAgICAgICAgICAgICAgICAgICBpc0ZpcnN0Q2hvaWNlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0OiBcImNvcnJlY3RfbWFya1wiXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIHBsYXllclRvdGFsU2NvcmVzOiB7XG4gICAgICAgICAgICAgICAgXCJwbGF5ZXJfMDAxXCI6IDgsIC8vIOWOn+adpTbliIYgKyDmnKzlm57lkIgy5YiGXG4gICAgICAgICAgICAgICAgXCJwbGF5ZXJfMDAyXCI6IDQgIC8vIOWOn+adpTPliIYgKyDmnKzlm57lkIgx5YiGXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgcmVtYWluaW5nTWluZXM6IDksIC8vIOWJqeS9meeCuOW8ueaVsOmHj1xuICAgICAgICAgICAgbWVzc2FnZTogXCLlsZXnpLrpmLbmrrXvvJrlhYjmiYvnjqnlrrbkuI3lupTlnKhwbGF5ZXJfZ2FtZV9wZmLkuK3mmL7npLrliqDlh4/liIZcIlxuICAgICAgICB9O1xuXG4gICAgICAgIGNvbnN0IG1lc3NhZ2VCZWFuID0ge1xuICAgICAgICAgICAgbXNnSWQ6IE1lc3NhZ2VJZC5Nc2dUeXBlTm90aWNlQWN0aW9uRGlzcGxheSxcbiAgICAgICAgICAgIGNvZGU6IDAsXG4gICAgICAgICAgICBtc2c6IFwic3VjY2Vzc1wiLFxuICAgICAgICAgICAgZGF0YTogdGVzdERhdGFcbiAgICAgICAgfTtcblxuICAgICAgICBHYW1lTWdyLkV2ZW50LlNlbmQoRXZlbnRUeXBlLlJlY2VpdmVNZXNzYWdlLCBtZXNzYWdlQmVhbik7XG5cbiAgICAgICAgaWYgKHRoaXMuc3RhdHVzTGFiZWwpIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdHVzTGFiZWwuc3RyaW5nID0gYOW3suWPkemAgUFjdGlvbkRpc3BsYXnmtojmga86IOWFiOaJi+eOqeWutuS4jeW6lOaYvuekunBsYXllcl9nYW1lX3BmYuWKoOWHj+WIhmA7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBvbkRlc3Ryb3koKSB7XG4gICAgICAgIGlmICh0aGlzLnRlc3RCdXR0b24pIHtcbiAgICAgICAgICAgIHRoaXMudGVzdEJ1dHRvbi5ub2RlLm9mZignY2xpY2snLCB0aGlzLnNlbmRUZXN0TWVzc2FnZSwgdGhpcyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuZmlyc3RDaG9pY2VUZXN0QnV0dG9uKSB7XG4gICAgICAgICAgICB0aGlzLmZpcnN0Q2hvaWNlVGVzdEJ1dHRvbi5ub2RlLm9mZignY2xpY2snLCB0aGlzLnRlc3RGaXJzdENob2ljZUJvbnVzRmxvdywgdGhpcyk7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXX0=