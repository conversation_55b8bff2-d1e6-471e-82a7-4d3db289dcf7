
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //棋盘控制器
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        console.log("🎮🎮🎮 收到GameStart消息，开始重置游戏场景 🎮🎮🎮");
        console.log("GameStart数据:", data);
        // 重置游戏场景：清除数字、炸弹和标记的预制体，重新显示小格子
        if (this.chessBoardController) {
            console.log("✅ chessBoardController 存在，开始调用 resetGameScene");
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
        // 重置游戏状态
        this.canOperate = false;
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.currentCountdown = 0;
        this.gameStatus = 0;
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1 && data.validHexCoords) {
            // 六边形地图，暂时使用默认值，后续可根据实际需求调整
            this.currentMineCount = Math.floor(data.validHexCoords.length * 0.15); // 约15%的格子是炸弹
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
        console.log("✅ GameStart处理完成，游戏场景已重置");
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        console.log("🧪 手动测试重置功能");
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.chessBoardController) {
            this.chessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        var _this = this;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 更新剩余炸弹数量显示
        if (data.remainingMines !== undefined) {
            this.updateMineCountDisplay(data.remainingMines);
        }
        // 在棋盘上显示所有玩家的操作
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
        // 使用setTimeout作为备选方案
        setTimeout(function () {
            _this.updateBoardAfterActions(data);
        }, 1000);
        // 同时也使用scheduleOnce
        this.scheduleOnce(this.delayedUpdateBoard.bind(this, data), 1.0);
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 延迟1秒后更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 第一步：先让所有头像消失
        var _this = this;
        this.hideAllAvatars(data.playerActions, function () {
            // 头像消失完成后，执行后续操作
            // 第二步：处理每个玩家的操作结果
            // 先按位置分组，处理同一位置有多个操作的情况
            var processedPositions = new Set();
            data.playerActions.forEach(function (action) {
                var positionKey = action.x + "," + action.y;
                // 如果这个位置已经处理过，跳过
                if (processedPositions.has(positionKey)) {
                    return;
                }
                // 查找同一位置的所有操作
                var samePositionActions = data.playerActions.filter(function (a) {
                    return a.x === action.x && a.y === action.y;
                });
                // 处理同一位置的操作结果
                _this.processPositionResult(action.x, action.y, samePositionActions);
                // 标记这个位置已处理
                processedPositions.add(positionKey);
            });
            // 第三步：处理连锁展开结果
            if (data.floodFillResults && data.floodFillResults.length > 0) {
                data.floodFillResults.forEach(function (floodFill) {
                    _this.processFloodFillResult(floodFill);
                });
            }
        });
    };
    /**
     * 让所有头像消失（简化版：直接删除所有头像）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 直接调用一次头像删除，不区分位置
        this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
            onComplete();
        });
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _a, _b;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 为每个连锁格子播放消失动画
        floodFill.revealedBlocks.forEach(function (block, index) {
            // 延迟播放动画，创造连锁效果
            _this.scheduleOnce(function () {
                _this.chessBoardController.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
            }, index * 0.1); // 每个格子间隔0.1秒
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.chessBoardController) {
            this.chessBoardController.clearAllPlayerNodes();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        if (!this.chessBoardController || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
        if (playerTotalScores) {
            this.displayPlayerScoreAnimationsAndUpdateTotalScores(playerActions, playerTotalScores);
        }
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.chessBoardController) {
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.chessBoardController) {
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.chessBoardController) {
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("ChessBoardController未设置，无法显示player_game_pfb加分效果");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            _this.updateCountdownDisplay(remainingSeconds);
            if (remainingSeconds <= 0) {
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            if (seconds > 0) {
                this.timeLabel.string = seconds + "s"; // 显示数字加s：5s, 4s, 3s, 2s, 1s
            }
            else {
                this.timeLabel.string = ""; // 不显示0，显示空白
            }
        }
        this.currentCountdown = seconds;
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else {
            console.warn("chessBoardController 不存在，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
        // 第三步：在player_game_pfb中显示本回合的加减分（与非先手玩家同步）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    GamePageController.prototype.onDestroy = function () {
        // 移除棋盘点击事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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