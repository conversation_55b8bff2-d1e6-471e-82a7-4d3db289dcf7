{"version": 3, "sources": ["assets/scripts/test/NoticeRoundStartTest.ts"], "names": [], "mappings": ";;;;;AAAA,4BAA4B;AAC5B,6CAA6C;;;;;;;;;;;;;;;;;;;;;AAE7C,6CAA4C;AAC5C,qDAAkD;AAClD,8CAA6C;AAGvC,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAkD,wCAAY;IAA9D;QAAA,qEA2QC;QAxQG,gBAAU,GAAc,IAAI,CAAC;QAG7B,2BAAqB,GAAc,IAAI,CAAC;QAGxC,iBAAW,GAAa,IAAI,CAAC;;IAkQjC,CAAC;IAhQG,oCAAK,GAAL;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SAChE;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;SACpF;QAED,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC;SACxC;IACL,CAAC;IAED,0BAA0B;IAC1B,8CAAe,GAAf;QACI,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QAEtC,SAAS;QACT,IAAM,QAAQ,GAAqB;YAC/B,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,CAAC;SAChB,CAAC;QAEF,aAAa;QACb,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,uBAAuB;YACxC,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,SAAS;QACT,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,6DAAc,QAAQ,CAAC,WAAW,4BAAQ,QAAQ,CAAC,SAAS,WAAG,CAAC;SAC7F;IACL,CAAC;IAED,YAAY;IACZ,kDAAmB,GAAnB,UAAoB,OAAe;QAC/B,IAAM,QAAQ,GAAqB;YAC/B,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,OAAO;YAClB,UAAU,EAAE,CAAC;SAChB,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,uBAAuB;YACxC,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,qCAAU,OAAO,WAAG,CAAC;SAClD;IACL,CAAC;IAED,YAAY;IACZ,sDAAuB,GAAvB;QAAA,iBAoBC;QAnBG,WAAW;QACX,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC;IAED,4BAA4B;IAC5B,uDAAwB,GAAxB;QACI,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QAEzC,IAAM,QAAQ,GAAwB;YAClC,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE;gBACX;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,IAAI;oBACnB,MAAM,EAAE,CAAC,CAAC,MAAM;iBACnB;gBACD;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,KAAK;oBACpB,MAAM,EAAE,cAAc;iBACzB;aACJ;YACD,iBAAiB,EAAE;gBACf,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC;aAClB;YACD,cAAc,EAAE,EAAE;YAClB,OAAO,EAAE,eAAe;SAC3B,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,0BAA0B;YAC3C,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,4FAA8B,QAAQ,CAAC,SAAS,4CAAS,QAAQ,CAAC,cAAc,WAAG,CAAC;SACjH;IACL,CAAC;IAED,YAAY;IACZ,gDAAiB,GAAjB;QAAA,iBAYC;QAXG,YAAY;QACZ,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC;SAC7C;IACL,CAAC;IAED,WAAW;IACX,uDAAwB,GAAxB;QAAA,iBAmBC;QAlBG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE1B,YAAY;QACZ,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,eAAe;QACf,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,uBAAuB;QACvB,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,uCAAuC,EAAE,CAAC;QACnD,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC;SAC7C;IACL,CAAC;IAED,+BAA+B;IAC/B,0DAA2B,GAA3B;QACI,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAE5C,IAAM,QAAQ,GAA2B;YACrC,MAAM,EAAE,YAAY;YACpB,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,CAAC,CAAC,gBAAgB;SACjC,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,6BAA6B;YAC9C,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,kGAA2C,CAAC;SACzE;IACL,CAAC;IAED,mCAAmC;IACnC,sEAAuC,GAAvC;QACI,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAE9C,IAAM,QAAQ,GAAwB;YAClC,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE;gBACX;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,IAAI;oBACnB,MAAM,EAAE,CAAC,CAAC,MAAM;iBACnB;gBACD;oBACI,MAAM,EAAE,YAAY;oBACpB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE,CAAC;oBACR,aAAa,EAAE,KAAK;oBACpB,MAAM,EAAE,cAAc;iBACzB;aACJ;YACD,iBAAiB,EAAE;gBACf,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC,CAAE,eAAe;aACnC;YACD,cAAc,EAAE,CAAC;YACjB,OAAO,EAAE,mCAAmC;SAC/C,CAAC;QAEF,IAAM,WAAW,GAAG;YAChB,KAAK,EAAE,qBAAS,CAAC,0BAA0B;YAC3C,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,QAAQ;SACjB,CAAC;QAEF,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,gIAAgD,CAAC;SAC9E;IACL,CAAC;IAED,wCAAS,GAAT;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SACjE;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;SACrF;IACL,CAAC;IAvQD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;uEACoB;IAGxC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;6DACU;IATZ,oBAAoB;QADxC,OAAO;OACa,oBAAoB,CA2QxC;IAAD,2BAAC;CA3QD,AA2QC,CA3QiD,EAAE,CAAC,SAAS,GA2Q7D;kBA3QoB,oBAAoB", "file": "", "sourceRoot": "/", "sourcesContent": ["// 测试NoticeRoundStart消息处理的脚本\n// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能\n\nimport { GameMgr } from \"../common/GameMgr\";\nimport { EventType } from \"../common/EventCenter\";\nimport { MessageId } from \"../net/MessageId\";\nimport { NoticeRoundStart, NoticeActionDisplay, NoticeFirstChoiceBonus } from \"../bean/GameBean\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class NoticeRoundStartTest extends cc.Component {\n\n    @property(cc.Button)\n    testButton: cc.Button = null;\n\n    @property(cc.Button)\n    firstChoiceTestButton: cc.Button = null;\n\n    @property(cc.Label)\n    statusLabel: cc.Label = null;\n\n    start() {\n        if (this.testButton) {\n            this.testButton.node.on('click', this.sendTestMessage, this);\n        }\n\n        if (this.firstChoiceTestButton) {\n            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);\n        }\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '点击按钮测试消息';\n        }\n    }\n\n    // 发送测试的NoticeRoundStart消息\n    sendTestMessage() {\n        console.log('发送测试NoticeRoundStart消息');\n        \n        // 创建测试数据\n        const testData: NoticeRoundStart = {\n            roundNumber: 1,\n            countDown: 25,\n            gameStatus: 0\n        };\n\n        // 模拟接收到的消息格式\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeRoundStart,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        // 发送消息事件\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n        \n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送测试消息: 回合${testData.roundNumber}, 倒计时${testData.countDown}秒`;\n        }\n    }\n\n    // 发送倒计时更新测试\n    sendCountdownUpdate(seconds: number) {\n        const testData: NoticeRoundStart = {\n            roundNumber: 1,\n            countDown: seconds,\n            gameStatus: 0\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeRoundStart,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n        \n        if (this.statusLabel) {\n            this.statusLabel.string = `倒计时更新: ${seconds}秒`;\n        }\n    }\n\n    // 测试不同的倒计时值\n    testDifferentCountdowns() {\n        // 测试25秒倒计时\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(25);\n        }, 1);\n\n        // 测试20秒倒计时（进入展示阶段）\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(20);\n        }, 3);\n\n        // 测试5秒倒计时（回合结束前）\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(5);\n        }, 5);\n\n        // 测试0秒倒计时（回合结束）\n        this.scheduleOnce(() => {\n            this.sendCountdownUpdate(0);\n        }, 7);\n    }\n\n    // 发送NoticeActionDisplay测试消息\n    sendActionDisplayMessage() {\n        console.log('发送测试NoticeActionDisplay消息');\n\n        const testData: NoticeActionDisplay = {\n            roundNumber: 1,\n            gameStatus: 0,\n            countDown: 5,\n            playerActions: [\n                {\n                    userId: \"player_001\",\n                    x: 3,\n                    y: 2,\n                    action: 1, // 挖掘\n                    score: 1,\n                    isFirstChoice: true,\n                    result: 2 // 数字2\n                },\n                {\n                    userId: \"player_002\",\n                    x: 1,\n                    y: 4,\n                    action: 2, // 标记\n                    score: 1,\n                    isFirstChoice: false,\n                    result: \"correct_mark\"\n                }\n            ],\n            playerTotalScores: {\n                \"player_001\": 5,\n                \"player_002\": 3\n            },\n            remainingMines: 10, // 剩余炸弹数量\n            message: \"展示阶段：显示所有玩家操作\"\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeActionDisplay,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送ActionDisplay消息: 展示阶段，剩余${testData.countDown}秒，剩余炸弹${testData.remainingMines}个`;\n        }\n    }\n\n    // 测试完整的回合流程\n    testFullRoundFlow() {\n        // 1. 发送回合开始\n        this.sendTestMessage();\n\n        // 2. 20秒后发送操作展示\n        this.scheduleOnce(() => {\n            this.sendActionDisplayMessage();\n        }, 2);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '开始测试完整回合流程...';\n        }\n    }\n\n    // 测试先手奖励流程\n    testFirstChoiceBonusFlow() {\n        console.log('开始测试先手奖励流程');\n\n        // 1. 发送回合开始\n        this.sendTestMessage();\n\n        // 2. 2秒后发送先手奖励\n        this.scheduleOnce(() => {\n            this.sendFirstChoiceBonusMessage();\n        }, 2);\n\n        // 3. 4秒后发送操作展示（包含先手玩家）\n        this.scheduleOnce(() => {\n            this.sendActionDisplayWithFirstChoiceMessage();\n        }, 4);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = '开始测试先手奖励流程...';\n        }\n    }\n\n    // 发送NoticeFirstChoiceBonus测试消息\n    sendFirstChoiceBonusMessage() {\n        console.log('发送测试NoticeFirstChoiceBonus消息');\n\n        const testData: NoticeFirstChoiceBonus = {\n            userId: \"player_001\",\n            roundNumber: 1,\n            bonusScore: 1,\n            totalScore: 6 // 原来5分 + 1分先手奖励\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeFirstChoiceBonus,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送FirstChoiceBonus消息: player_001获得+1先手奖励`;\n        }\n    }\n\n    // 发送包含先手玩家的NoticeActionDisplay测试消息\n    sendActionDisplayWithFirstChoiceMessage() {\n        console.log('发送包含先手玩家的NoticeActionDisplay消息');\n\n        const testData: NoticeActionDisplay = {\n            roundNumber: 1,\n            gameStatus: 0,\n            countDown: 5,\n            playerActions: [\n                {\n                    userId: \"player_001\",\n                    x: 3,\n                    y: 2,\n                    action: 1, // 挖掘\n                    score: 2, // 本回合得分\n                    isFirstChoice: true, // 先手玩家\n                    result: 3 // 数字3\n                },\n                {\n                    userId: \"player_002\",\n                    x: 1,\n                    y: 4,\n                    action: 2, // 标记\n                    score: 1,\n                    isFirstChoice: false,\n                    result: \"correct_mark\"\n                }\n            ],\n            playerTotalScores: {\n                \"player_001\": 8, // 原来6分 + 本回合2分\n                \"player_002\": 4  // 原来3分 + 本回合1分\n            },\n            remainingMines: 9, // 剩余炸弹数量\n            message: \"展示阶段：先手玩家不应在player_game_pfb中显示加减分\"\n        };\n\n        const messageBean = {\n            msgId: MessageId.MsgTypeNoticeActionDisplay,\n            code: 0,\n            msg: \"success\",\n            data: testData\n        };\n\n        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);\n\n        if (this.statusLabel) {\n            this.statusLabel.string = `已发送ActionDisplay消息: 先手玩家不应显示player_game_pfb加减分`;\n        }\n    }\n\n    onDestroy() {\n        if (this.testButton) {\n            this.testButton.node.off('click', this.sendTestMessage, this);\n        }\n        if (this.firstChoiceTestButton) {\n            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);\n        }\n    }\n}\n"]}