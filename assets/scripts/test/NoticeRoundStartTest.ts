// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能

import { GameMgr } from "../common/GameMgr";
import { EventType } from "../common/EventCenter";
import { MessageId } from "../net/MessageId";
import { NoticeRoundStart, NoticeActionDisplay, NoticeFirstChoiceBonus } from "../bean/GameBean";

const { ccclass, property } = cc._decorator;

@ccclass
export default class NoticeRoundStartTest extends cc.Component {

    @property(cc.Button)
    testButton: cc.Button = null;

    @property(cc.Button)
    firstChoiceTestButton: cc.Button = null;

    @property(cc.Label)
    statusLabel: cc.Label = null;

    start() {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }

        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);
        }

        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试消息';
        }
    }

    // 发送测试的NoticeRoundStart消息
    sendTestMessage() {
        console.log('发送测试NoticeRoundStart消息');
        
        // 创建测试数据
        const testData: NoticeRoundStart = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };

        // 模拟接收到的消息格式
        const messageBean = {
            msgId: MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };

        // 发送消息事件
        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);
        
        if (this.statusLabel) {
            this.statusLabel.string = `已发送测试消息: 回合${testData.roundNumber}, 倒计时${testData.countDown}秒`;
        }
    }

    // 发送倒计时更新测试
    sendCountdownUpdate(seconds: number) {
        const testData: NoticeRoundStart = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };

        const messageBean = {
            msgId: MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };

        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);
        
        if (this.statusLabel) {
            this.statusLabel.string = `倒计时更新: ${seconds}秒`;
        }
    }

    // 测试不同的倒计时值
    testDifferentCountdowns() {
        // 测试25秒倒计时
        this.scheduleOnce(() => {
            this.sendCountdownUpdate(25);
        }, 1);

        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(() => {
            this.sendCountdownUpdate(20);
        }, 3);

        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(() => {
            this.sendCountdownUpdate(5);
        }, 5);

        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(() => {
            this.sendCountdownUpdate(0);
        }, 7);
    }

    // 发送NoticeActionDisplay测试消息
    sendActionDisplayMessage() {
        console.log('发送测试NoticeActionDisplay消息');

        const testData: NoticeActionDisplay = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1, // 挖掘
                    score: 1,
                    isFirstChoice: true,
                    result: 2 // 数字2
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2, // 标记
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 5,
                "player_002": 3
            },
            remainingMines: 10, // 剩余炸弹数量
            message: "展示阶段：显示所有玩家操作"
        };

        const messageBean = {
            msgId: MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };

        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);

        if (this.statusLabel) {
            this.statusLabel.string = `已发送ActionDisplay消息: 展示阶段，剩余${testData.countDown}秒，剩余炸弹${testData.remainingMines}个`;
        }
    }

    // 测试完整的回合流程
    testFullRoundFlow() {
        // 1. 发送回合开始
        this.sendTestMessage();

        // 2. 20秒后发送操作展示
        this.scheduleOnce(() => {
            this.sendActionDisplayMessage();
        }, 2);

        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    }

    // 测试先手奖励流程
    testFirstChoiceBonusFlow() {
        console.log('开始测试先手奖励流程');

        // 1. 发送回合开始
        this.sendTestMessage();

        // 2. 2秒后发送先手奖励
        this.scheduleOnce(() => {
            this.sendFirstChoiceBonusMessage();
        }, 2);

        // 3. 4秒后发送操作展示（包含先手玩家）
        this.scheduleOnce(() => {
            this.sendActionDisplayWithFirstChoiceMessage();
        }, 4);

        if (this.statusLabel) {
            this.statusLabel.string = '开始测试先手奖励流程...';
        }
    }

    // 发送NoticeFirstChoiceBonus测试消息
    sendFirstChoiceBonusMessage() {
        console.log('发送测试NoticeFirstChoiceBonus消息');

        const testData: NoticeFirstChoiceBonus = {
            userId: "player_001",
            roundNumber: 1,
            bonusScore: 1,
            totalScore: 6 // 原来5分 + 1分先手奖励
        };

        const messageBean = {
            msgId: MessageId.MsgTypeNoticeFirstChoiceBonus,
            code: 0,
            msg: "success",
            data: testData
        };

        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);

        if (this.statusLabel) {
            this.statusLabel.string = `已发送FirstChoiceBonus消息: player_001获得+1先手奖励`;
        }
    }

    // 发送包含先手玩家的NoticeActionDisplay测试消息
    sendActionDisplayWithFirstChoiceMessage() {
        console.log('发送包含先手玩家的NoticeActionDisplay消息');

        const testData: NoticeActionDisplay = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1, // 挖掘
                    score: 2, // 本回合得分
                    isFirstChoice: true, // 先手玩家
                    result: 3 // 数字3
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2, // 标记
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 8, // 原来6分 + 本回合2分
                "player_002": 4  // 原来3分 + 本回合1分
            },
            remainingMines: 9, // 剩余炸弹数量
            message: "展示阶段：先手玩家仍然要在player_game_pfb中显示本回合加减分"
        };

        const messageBean = {
            msgId: MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };

        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);

        if (this.statusLabel) {
            this.statusLabel.string = `已发送ActionDisplay消息: 先手玩家仍然显示player_game_pfb加减分`;
        }
    }

    onDestroy() {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);
        }
    }
}
