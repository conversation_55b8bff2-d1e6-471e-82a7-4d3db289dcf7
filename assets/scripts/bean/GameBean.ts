


export interface LoginData {
    userInfo: UserInfo;
    roomId: number; //这个房间号>0 的话 就是已经在游戏中断线重连的
    inviteCode: number; //这个邀请码>0 的话 就是已经创建好房间 之后断线重连的
    roomConfigs: RoomConfig[];
}

export interface RoomConfig {
    id: number;//房间类型 1-普通场 2-私人场
    fees: number[];// 入场费列表
    playerNums: number[];// 玩家人数列表
}

export interface UserInfo {
    userId: string;// 玩家ID
    nickname: string;// 昵称
    avatar: string; // 头像
    coin: number;// 当前金币
    serverTime: number;// 服务器的秒级时间戳

}

//请求匹配
export interface PairRequest {
    playerNum: number;// 玩家人数
    fee: number;// 房间费
}

//创建邀请的请求
export interface CreateInvite {
    playerNum: number;// 玩家人数
    fee: number;// 房间费
}

export interface InviteInfo {
    inviteCode: number;// 邀请码
    playerNum: number;// 玩家人数
    fee: number;// 房间费
    propMode: number;// 0 无道具 1 有道具
    creatorId: string;// 创建者用户ID
    users: InviteUser[];// 匹配到的所有玩家
}

export interface InviteUser {
    userId: string;// 玩家Id
    nickname: string;// 昵称
    avatar: string;// 头像
    ready: boolean;// 是否准备好
    creator: boolean;// 是否邀请的发起者
    onLine: boolean;// 是否在线
}


//接受邀请的请求
export interface AcceptInvite {
    inviteCode: number;// 邀请码
}

// AcceptInvite 接受邀请
export interface AcceptInvite {
    userId: string;// 用户ID
    inviteInfo: InviteInfo;// 邀请信息
}

// InviteReady 邀请者改变准备状态
export interface InviteReady {
    ready: boolean;// true 准备 false 取消准备
}

// NoticeUserInviteStatus 广播玩家的邀请状态˝
export interface NoticeUserInviteStatus {
    userId: string;// 玩家Id
    ready: boolean;// 是否准备好
    onLine: boolean;// 是否在线
}

// ChangeInviteCfg 更改邀请配置
export interface ChangeInviteCfgRequest {
    inviteCode: number;// 邀请码
    fee: number;// 房间费
}

export interface ChangeInviteCfgResult {
    fee: number;// 房间费
}

// NoticeLeaveInvite 邀请广播有人离开
export interface NoticeLeaveInvite {
    userId: string;// 玩家Id
    isCreator: boolean;// 离开者是否是创建者
}

//创建者踢出玩家请求
export interface InviteKickOut {
    userId: string // 被踢除的玩家Id
}

//通知邀请状态
export interface NoticeUserInviteStatus {
    userId: string  //用户 id
    ready: boolean //准备状态
    onLine: boolean //是否在线
}


//////////////////////////////////////////游戏内的数据//////////////////////////////////////////

//开始游戏和 重连都走这一个
export interface NoticeStartGame {

    roomId: number;// 房间ID
    roomType: number; // 房间类型  EnumBean.RoomType
    playerNum: number;// 玩家人数
    mapType: number;// 地图类型 0-方形地图，1-六边形地图
    fee: number; // 房间费
    propMode: number;// 道具模式
    specialFull: number;// 多少特殊块填满技能槽
    specialRemoved: number;// 已移除特殊块数量
    users: RoomUser[];// 匹配到的所有玩家
    gameStatus: number;// 游戏状态  EnumBean.GameStatus
    countDown: number;// 游戏状态倒计时
    blockList: Block[];// 地图
    opIndex: number;// 操作索引
    lookPos: number;// 旁观座位号
    curTask: CurTask// 当前任务
    validHexCoords?: HexCoord[];// 有效的六边形坐标列表（仅mapType=1时返回）
    mapConfig?: MapConfig;// 地图配置信息（仅mapType=0时返回）

}

export interface RoomUser {
    userId: string;// 用户ID
    nickName: string;// 昵称
    avatar: string;// 头像
    pos: number;// 座位号
    coin: number;// 玩家最新金币
    status: number;// 玩家状态   EnumBean.UserStatus
    score: number;// 消除分数
    rank: number //当前排名
}

// 地图配置信息（方形地图专用）
export interface MapConfig {
    width: number;// 地图宽度
    height: number;// 地图高度
    mineCount: number;// 地雷总数
}

// 六边形坐标结构（六边形地图专用）
export interface HexCoord {
    q: number;// 六边形坐标系q坐标
    r: number;// 六边形坐标系r坐标
}

export interface Block {
    id: number; // 块的ID
    color: number; // 块颜色 EnumBean.BlockColor  1-红色 2-蓝色 3-绿色 4-黄色 5-紫色
    type: number; // 块类型 EnumBean.BlockType 1-普通块 2-箭头X 3-箭头Y 4-炸弹 5-彩虹 6-超级箭头 7-炸弹箭头 8-彩虹箭头 9-超级炸弹 10-彩虹炸弹 11-超级彩虹
    obstacle: Obstacle; // 障碍物  
}

export interface Obstacle {
    type: number; //EnumBean.Obstacle
    fromUid: string //如果 type==1002 的话会有两个用户 id 用，隔开，前面的id是发射锁链的用户后面的id是发射冰块的用户

}
// ObstacleBlock 障碍物的块
export interface ObstacleBlock {
    id: number; // 旧的块ID(未掉落前的位置)
    obstacle: Obstacle // 障碍物 （这里代表消除障碍物之后 剩下的状态）
}


export interface CurTask {
    id: number;// 任务ID
    color: number;// 目标颜色
    require: number;// 需要完成的数量
    current: number;// 当前进度
    reward: number;// 奖励(1-彩虹、2-冰块、3-锁链、4-箭头X、5-箭头Y)
}


//移动块-MoveBlock 的请求数据   
export interface MoveBlock {
    id: number;// 块ID
    otherId: number;// 另一个块ID
}

// NoticeScoreChg 通知分数变化(消息ID-ScoreChg)
export interface NoticeScoreChg {
    userId: string;// 玩家Id
    score: number;// 新的分数
    taskObstacle: ObstacleBlock // 障碍物

}
// MoveBlockFail 移动块失败
export interface MoveBlockFail {
    id: number;// 块ID
    otherId: number;// 另一个块ID
}

// NoticeMoveBlock 通知移动块
export interface NoticeMoveBlock {
    userId: string;// 玩家Id
    id: number;// 块ID
    otherId: number;// 另一个块ID
    score: number;// 新的分数
    specialRemoved: number;// 新的已移除特殊块数量
    groups: MoveGroup[];// 匹配分组的列表
    activate: Activate;// 激活特殊道具
    opIndex: number;// 操作索引
    isTaskOver: boolean// // 是否旧任务结束
    taskReward: TaskReward// // 任务奖励
    curTask: CurTask// 当前任务
    obstacles:ObstacleBlock[] // 障碍物 (在这里没啥用  就是用来打印数据的)
}

// TaskReward 任务奖励
export interface TaskReward {
    id: number;// 块ID
    type: number;// 块类型 EnumBean.BlockType

}

// NoticeActivate 通知激活特殊道具
export interface Activate {
    times: number  // 激活次数
    rectIDs:number[] //激活区域四个块 中左上角的块
    groups: MoveGroup[] // 匹配分组的列表
}

// MoveGroup 匹配分组
export interface MoveGroup {
    matches: Match[];// 匹配列表
    drops: DropBlock[];// 新掉落的块列表
    scoreChg: number;// 分数变化
    obstaclesChg:ObstacleBlock[] //
}

export interface DropBlock {
    id: number; // 块的ID
    color: number; // 块颜色 EnumBean.BlockColor  1-红色 2-蓝色 3-绿色 4-黄色 5-紫色
}

export interface UserSettlement {
    userId: string;// 玩家Id
    coinChg: number;// 金币变化
    coin: number;// 玩家最新金币
    rank: number;// 排名
    score: number;// 分数
}

// Match 单个匹配
export interface Match {
    removes: Remove[];// 移动后可消除的块列表(也可能两个特殊块叠一起，不会构成>=3的同颜色消除)
    merge: MergeBean;// 合成块
    passives: Passive[];// 被动消除列表(Removes中有N个特殊块)
}

export interface MergeBean{
    id: number;// 块ID
    type:null;// 块类型 EnumBean.BlockType
}

// Passive 被动消除
export interface Passive {
    id: number;// 块ID
    type: number;// 块类型 EnumBean.BlockType
    score: number;// 得分
    obstacle: number;// 障碍物
    removes: Remove[];// 消除的块列表
    passives: Passive[];// 被动消除列表(Removes中有N个特殊块)
}

// PassiveRemove 被动消除移除的块
export interface Remove {
    id: number;// 块ID
    score: number;// 得分
}

export interface NoticeSettlement {
    users?: UserSettlement[];// 闲家结算列表（旧版本兼容）
    finalRanking?: PlayerFinalResult[];// 最终排名列表（扫雷游戏）
    fee?: number;// 房间费用
    gameStats?: GameStats;// 游戏统计信息
    gameType?: string;// 游戏类型
    playerCount?: number;// 玩家数量
    totalRounds?: number;// 总回合数
}

export interface UserSettlement {
    userId: string;// 玩家Id
    coinChg: number;// 金币变化
    coin: number;// 玩家最新金币
    rank: number;// 排名
    score: number;// 分数
}

// 游戏统计信息
export interface GameStats {
    mapSize: string;// 地图大小
    mineCount: number;// 地雷数量
    revealedCount: number;// 已揭示方块数量
}

//玩家主动离开房间的请求数据
export interface LeaveRoom {
    isConfirmLeave: boolean;// 确认离开房间
}

//玩家主动离开房间-LeaveRoom
export interface NoticeLeaveRoom {
    userId: string;// 玩家Id
}

//玩家被踢出房间-KickOutUser
export interface NoticeUserKickOut {
    userId: string;// 玩家Id
}

export interface IllegalOperation {
    id: number; //移动块
    otherId: number; //被交换块
    tokenUserId: string; //当前操作用户的id，不是此次操作用户的 id
}

// 扫雷游戏相关接口定义

// 扫雷回合开始通知
export interface NoticeRoundStart {
    roundNumber: number; // 回合编号（从1开始）
    countDown: number; // 回合倒计时（25秒）
    gameStatus: number; // 游戏状态（0-扫雷进行中）
}

// 扫雷操作展示通知
export interface NoticeActionDisplay {
    roundNumber: number; // 当前回合编号
    gameStatus: number; // 游戏状态（0-扫雷进行中）
    countDown: number; // 剩余倒计时（5秒展示阶段）
    playerActions: PlayerActionDisplay[]; // 玩家操作展示列表
    floodFillResults?: FloodFillResult[]; // 连锁展开结果列表（可选）
    playerTotalScores: {[userId: string]: number}; // 玩家累计总得分对象
    remainingMines: number; // 剩余炸弹数量
    message: string; // 提示信息
}

// 玩家操作展示结构
export interface PlayerActionDisplay {
    userId: string; // 玩家ID
    x: number; // 操作坐标x
    y: number; // 操作坐标y
    action: number; // 操作类型（1-挖掘，2-标记）
    score: number; // 本次操作得分
    isFirstChoice: boolean; // 是否为首选玩家
    result: number | string; // 操作结果（挖掘：数字或"mine"；标记："correct_mark"或"wrong_mark"）
}

// 扫雷回合结束通知
export interface NoticeRoundEnd {
    roundNumber: number; // 当前回合编号
    gameStatus: number; // 游戏状态（1-回合结束展示）
    countDown: number; // 回合结束展示倒计时（5秒）
    playerResults: PlayerRoundResult[]; // 玩家操作结果列表
    mapData: MineBlock[][]; // 地图数据（只显示已揭示的方块）
    floodFillResults?: FloodFillResult[]; // 连锁展开结果列表（可选）
}

// 玩家回合结果结构
export interface PlayerRoundResult {
    userId: string; // 玩家ID
    x: number; // 操作坐标x
    y: number; // 操作坐标y
    action: number; // 操作类型（1-挖掘，2-标记）
    score: number; // 本回合得分
    isFirstChoice: boolean; // 是否为首选玩家（享受+1分奖励）
    isMine: boolean; // 操作的方块是否是地雷
    neighborMines: number; // 操作方块周围的地雷数量
}

// 连锁展开结果结构
export interface FloodFillResult {
    revealedBlocks: RevealedBlock[]; // 连锁揭示的方块列表
    totalRevealed: number; // 总共揭示的方块数
    triggerUserId: string; // 触发连锁展开的玩家ID
    triggerX: number; // 触发点X坐标
    triggerY: number; // 触发点Y坐标
}

// 揭示方块结构
export interface RevealedBlock {
    x: number; // 方块X坐标
    y: number; // 方块Y坐标
    neighborMines: number; // 周围地雷数量
    isMine: boolean; // 是否是地雷
    triggerUserId: string; // 触发揭示的玩家ID
}

// 扫雷首选玩家奖励通知
export interface NoticeFirstChoiceBonus {
    userId: string; // 玩家ID
    roundNumber: number; // 回合编号
    bonusScore: number; // 首选玩家奖励分数（固定+1）
    totalScore: number; // 累计总得分（包含此奖励）
}

// 扫雷游戏结束通知
export interface NoticeGameEnd {
    gameStatus: number; // 游戏状态（2-游戏结束）
    countDown: number; // 游戏结束展示倒计时（10秒）
    finalRanking: PlayerFinalResult[]; // 最终排名列表
    completeMapData: MineBlock[][]; // 完整地图数据（显示所有地雷和信息）
    totalRounds: number; // 总回合数
}

// 玩家最终结果结构
export interface PlayerFinalResult {
    userId: string; // 玩家ID
    totalScore: number; // 总得分
    rank: number; // 最终排名
    coinChg: number; // 金币变化
    mineHits: number; // 踩雷次数
}

// 扫雷方块结构（已在API.md中定义，这里重新定义以确保类型一致）
export interface MineBlock {
    x: number; // x坐标（0-7）
    y: number; // y坐标（0-7）
    isRevealed: boolean; // 是否已揭开
    isMarked: boolean; // 是否被标记为地雷
    players: string[]; // 当前格子中的玩家ID列表
    NeighborMines: number; // 周围地雷数量(0-8)
    IsMine: boolean; // 是否是地雷
}

// 点击方块请求
export interface ClickBlockRequest {
    x: number; // 方块x坐标（0-7，从左到右）
    y: number; // 方块y坐标（0-7，从下到上，左下角为(0,0)）
    action: number; // 操作类型：1=挖掘方块，2=标记/取消标记地雷
}
